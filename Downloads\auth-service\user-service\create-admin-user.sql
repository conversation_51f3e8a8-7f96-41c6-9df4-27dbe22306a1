-- Create ADMIN user for the ITSM User Service
-- Run this after the application has started and created the tables
-- Connect: psql -U postgres -h localhost -d user_db -f create-admin-user.sql

-- Insert ADMIN user
INSERT INTO users (
    id,
    nom,
    prenom,
    email,
    role,
    statut_technicien,
    localisation,
    manager_id,
    team_id,
    actif,
    date_creation,
    date_modification
) VALUES (
    gen_random_uuid(),
    'Admin',
    'System',
    '<EMAIL>',
    'ADMIN',
    NULL,  -- ADMIN doesn't have technician status
    'Headquarters',
    NULL,  -- ADMIN has no manager
    NULL,  -- ADMIN is not assigned to a team
    true,
    NOW(),
    NOW()
);

-- Verify the ADMIN user was created
SELECT 
    id,
    nom,
    prenom,
    email,
    role,
    localisation,
    actif,
    date_creation
FROM users 
WHERE role = 'ADMIN';

-- Show all users (should only be the ADMIN for now)
SELECT 
    nom || ' ' || prenom as full_name,
    email,
    role,
    actif
FROM users
ORDER BY date_creation;
