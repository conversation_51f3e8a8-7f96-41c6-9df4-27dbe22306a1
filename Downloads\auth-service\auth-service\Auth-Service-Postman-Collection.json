{"info": {"name": "Auth Service API", "description": "Collection for testing the Auth Service with JWT authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8081", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "1. Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.token);", "    pm.test('Registration successful', function () {", "        pm.expect(response.token).to.not.be.empty;", "        pm.expect(response.role).to.eql('UTILISATEUR');", "    });", "} else {", "    pm.test('Registration failed', function () {", "        pm.expect(pm.response.code).to.be.oneOf([400, 409]);", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"<PERSON><PERSON>\",\n  \"prenom\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"motDePasse\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "2. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.token);", "    pm.test('Login successful', function () {", "        pm.expect(response.token).to.not.be.empty;", "        pm.expect(response.email).to.eql('<EMAIL>');", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        pm.expect(pm.response.code).to.be.oneOf([400, 401]);", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"motDePasse\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "3. Get User Profile (Protected)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.email).to.not.be.empty;", "    pm.expect(response.role).to.not.be.empty;", "});"]}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}}, {"name": "4. Register Duplicate User (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Duplicate registration rejected', function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 409]);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nom\": \"<PERSON><PERSON>\",\n  \"prenom\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"motDePasse\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "5. <PERSON><PERSON> with Wrong Password (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Wrong password rejected', function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 401]);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"motDePasse\": \"wrongpassword\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "6. Access Protected Route Without Token (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Unauthorized access rejected', function () {", "    pm.expect(pm.response.code).to.be.oneOf([401, 403]);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/profile", "host": ["{{baseUrl}}"], "path": ["user", "profile"]}}}]}