# Application Configuration
spring.application.name=eureka-server
server.port=8761

# Eureka Server Configuration
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/

# Disable security for development
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

# Logging
logging.level.com.netflix.eureka=INFO
logging.level.com.netflix.discovery=INFO
logging.level.org.springframework.cloud.netflix.eureka=INFO
