C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\persistence\repository\JpaUtilisateurRepository.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth_service\AuthServiceApplication.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\application\dto\RegisterUserRequest.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\interfaces\rest\GlobalExceptionHandler.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\util\BCryptHashGenerator.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\application\service\RegisterUserUseCase.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\persistence\entity\UtilisateurEntity.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\domain\model\Utilisateur.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\config\KafkaConfig.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\application\dto\LoginUserRequest.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\persistence\mapper\UtilisateurMapper.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\domain\repository\UtilisateurRepository.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\security\JwtProvider.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\application\dto\AuthResponse.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\interfaces\rest\UserController.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\security\JwtAuthenticationFilter.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\interfaces\rest\AuthController.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\domain\event\UserCreatedEvent.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\application\service\LoginUserUseCase.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\domain\model\Role.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\config\SecurityConfig.java
C:\Users\<USER>\Downloads\auth-service\auth-service\src\main\java\com\itsm\auth\infrastructure\persistence\adapter\UtilisateurRepositoryAdapter.java
