# Application Configuration
spring.application.name=auth-service
server.port=8081

# Database Configuration - using auth_db database
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=achrafmas03
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Configuration
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.idle-timeout=300000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.retries=0
spring.kafka.producer.request-timeout-ms=5000
spring.kafka.producer.delivery-timeout-ms=10000

# JWT Configuration
app.jwt.secret=mySecretKey123456789012345678901234567890123456789012345678901234567890
app.jwt.expiration=86400000

# Eureka Configuration
eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true
eureka.instance.hostname=localhost

# Logging
logging.level.com.itsm.auth=DEBUG
logging.level.org.springframework.security=DEBUG
